#!/bin/bash

# ConvNext Mobile Training Execution Script
# 在Android设备上执行ConvNext图像分类训练 - 支持多种batch size测试
#
# Usage:
#   ./3_training.sh                    # Run all batch sizes (1,4,8,16,32,64)
#   ./3_training.sh all                # Run all batch sizes (same as above)
#   ./3_training.sh 1 4 8              # Run specific batch sizes
#   ./3_training.sh 16                 # Run single batch size
#
# Supported batch sizes: 1, 4, 8, 16, 32, 64

set -e

# 显示帮助信息
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    echo "ConvNext Mobile Training Script"
    echo ""
    echo "Usage:"
    echo "  ./3_training.sh                    # Run all batch sizes (1,4,8,16,32,64)"
    echo "  ./3_training.sh all                # Run all batch sizes (same as above)"
    echo "  ./3_training.sh 1 4 8              # Run specific batch sizes"
    echo "  ./3_training.sh 16                 # Run single batch size"
    echo "  ./3_training.sh -h, --help         # Show this help"
    echo ""
    echo "Supported batch sizes: 1, 4, 8, 16, 32, 64"
    echo ""
    echo "Examples:"
    echo "  ./3_training.sh                    # Test all batch sizes"
    echo "  ./3_training.sh 1 4                # Test only batch size 1 and 4"
    echo "  ./3_training.sh 8                  # Test only batch size 8"
    exit 0
fi

# 设置设备目录
DEVICE_DIR="/data/local/tmp/convnext_training"

# 检查ADB连接
echo "=== Checking ADB Connection ==="
if ! adb devices | grep -q "device$"; then
    echo "❌ Error: No Android device connected via ADB"
    echo "Please connect your Android device and enable USB debugging"
    exit 1
fi

# 获取设备信息
DEVICE_MODEL=$(adb shell getprop ro.product.model 2>/dev/null | tr -d '\r' || echo "Unknown Device")
echo "✅ Connected to device: $DEVICE_MODEL"

# 支持的batch size列表
BATCH_SIZES=(1 4 8 16 32 64)

echo ""
echo "=== ConvNext Training Configuration ==="
echo "  Model: ConvNext (4 Stages, depths=[3,3,27,3])"
echo "  Input Size: 384x384"
echo "  Num Classes: 1000"
echo "  Backend: GPU (OpenCL)"
echo "  Precision: FP32"
echo "  Learning Rate: Constant (0.001)"
echo "  Loss Function: MSE Loss"
echo "  Supported Batch Sizes: ${BATCH_SIZES[*]}"
echo ""

# 解析命令行参数
SELECTED_BATCH_SIZES=()

if [ $# -eq 0 ] || [ "$1" = "all" ]; then
    # 默认运行所有batch size
    SELECTED_BATCH_SIZES=("${BATCH_SIZES[@]}")
    echo "🎯 Running all supported batch sizes: ${SELECTED_BATCH_SIZES[*]}"
else
    # 验证用户指定的batch size
    for arg in "$@"; do
        if [[ " ${BATCH_SIZES[*]} " =~ " $arg " ]]; then
            SELECTED_BATCH_SIZES+=("$arg")
        else
            echo "❌ Error: Unsupported batch size '$arg'"
            echo "Supported batch sizes: ${BATCH_SIZES[*]}"
            exit 1
        fi
    done
    echo "🎯 Running selected batch sizes: ${SELECTED_BATCH_SIZES[*]}"
fi

TOTAL_TESTS=${#SELECTED_BATCH_SIZES[@]}
echo "📊 Total tests to run: $TOTAL_TESTS"

# 创建结果日志文件
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RESULTS_LOG="convnext_batch_results_${TIMESTAMP}.log"

echo ""
echo "=== Starting ConvNext Training Tests ==="
echo "Results will be logged to: $RESULTS_LOG"
echo "Start time: $(date)"
echo ""

# 初始化统计
SUCCESSFUL_TESTS=0
FAILED_TESTS=0

for i in "${!SELECTED_BATCH_SIZES[@]}"; do
    batch_size=${SELECTED_BATCH_SIZES[$i]}
    test_num=$((i + 1))

    echo "--- Test $test_num/$TOTAL_TESTS: Batch Size $batch_size ---"
    echo "Test $test_num/$TOTAL_TESTS: Batch Size $batch_size" >> "$RESULTS_LOG"
    echo "Start time: $(date)" >> "$RESULTS_LOG"

    # 执行训练命令 (参数: 3=GPU OpenCL后端, batch_size)
    echo "Executing: cd $DEVICE_DIR && LD_LIBRARY_PATH=.:/vendor/lib64 ./runTrainDemo.out ConvNextMobileTraining ./real_test_data/train/ ./real_test_data/train.txt 3 $batch_size"
    echo ""

    # 记录开始时间
    start_time=$(date +%s)

    # 执行训练并捕获输出
    if adb shell "cd $DEVICE_DIR && LD_LIBRARY_PATH=.:/vendor/lib64 ./runTrainDemo.out ConvNextMobileTraining ./real_test_data/train/ ./real_test_data/train.txt 3 $batch_size" 2>&1 | tee -a "$RESULTS_LOG"; then
        # 计算耗时
        end_time=$(date +%s)
        duration=$((end_time - start_time))

        echo "✅ Batch size $batch_size completed successfully in ${duration}s" | tee -a "$RESULTS_LOG"
        SUCCESSFUL_TESTS=$((SUCCESSFUL_TESTS + 1))
    else
        end_time=$(date +%s)
        duration=$((end_time - start_time))

        echo "❌ Batch size $batch_size failed after ${duration}s" | tee -a "$RESULTS_LOG"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    echo "End time: $(date)" >> "$RESULTS_LOG"
    echo "Duration: ${duration}s" >> "$RESULTS_LOG"
    echo "----------------------------------------" >> "$RESULTS_LOG"
    echo ""

    # 短暂休息，让设备降温
    if [ $i -lt $((TOTAL_TESTS - 1)) ]; then
        echo "⏳ Cooling down for 5 seconds..."
        sleep 5
    fi
done

echo ""
echo "=== Training Tests Summary ==="
echo "Total tests: $TOTAL_TESTS"
echo "Successful: $SUCCESSFUL_TESTS"
echo "Failed: $FAILED_TESTS"
echo "Success rate: $(( SUCCESSFUL_TESTS * 100 / TOTAL_TESTS ))%"
echo ""
echo "Detailed results saved to: $RESULTS_LOG"
echo "End time: $(date)"

# 记录最终统计到日志
echo "" >> "$RESULTS_LOG"
echo "=== Final Summary ===" >> "$RESULTS_LOG"
echo "Total tests: $TOTAL_TESTS" >> "$RESULTS_LOG"
echo "Successful: $SUCCESSFUL_TESTS" >> "$RESULTS_LOG"
echo "Failed: $FAILED_TESTS" >> "$RESULTS_LOG"
echo "Success rate: $(( SUCCESSFUL_TESTS * 100 / TOTAL_TESTS ))%" >> "$RESULTS_LOG"
echo "End time: $(date)" >> "$RESULTS_LOG"

if [ $SUCCESSFUL_TESTS -eq $TOTAL_TESTS ]; then
    echo "🎉 All ConvNext training tests completed successfully!"
    exit 0
else
    echo "⚠️  Some tests failed. Check the log file for details."
    exit 1
fi