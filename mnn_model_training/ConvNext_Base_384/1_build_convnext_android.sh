#!/bin/bash

# ConvNext Android Training Build Script
# 专门用于编译ConvNext手机端训练程序

set -e

echo "=== ConvNext Android Training Build Script ==="

# 检查Android NDK环境
if [ -z "$ANDROID_NDK" ]; then
    export ANDROID_NDK=/home/<USER>/software/softwareDev/AndroidSDK/ndk/26.3.11579264
    echo "Setting ANDROID_NDK to: $ANDROID_NDK"
fi

if [ ! -d "$ANDROID_NDK" ]; then
    echo "Error: Android NDK not found at $ANDROID_NDK"
    echo "Please set ANDROID_NDK environment variable or install Android NDK"
    exit 1
fi

# 设置构建目录
BUILD_DIR="../../project/android/build_64"
MNN_ROOT="../../"
CONVNEXT_DIR="$(pwd)"

echo "Android NDK: $ANDROID_NDK"
echo "Build Directory: $BUILD_DIR"
echo "MNN Root: $MNN_ROOT"
echo "ConvNext Directory: $CONVNEXT_DIR"

echo ""
echo "=== Synchronizing ConvNext Training Code ==="

# 确保训练代码在正确的编译位置（在改变目录之前）
DEMO_DIR="../../tools/train/source/demo"
CONVNEXT_SOURCE="ConvNextTrainingDemo.cpp"

if [ -f "$CONVNEXT_SOURCE" ]; then
    echo "Found ConvNext training code: $CONVNEXT_SOURCE"
    echo "Copying to demo directory: $DEMO_DIR/"

    # 创建备份（如果目标文件已存在）
    if [ -f "$DEMO_DIR/ConvNextTrainingDemo.cpp" ]; then
        echo "Creating backup of existing file..."
        cp "$DEMO_DIR/ConvNextTrainingDemo.cpp" "$DEMO_DIR/ConvNextTrainingDemo.cpp.backup"
    fi

    # 复制新的训练代码
    cp "$CONVNEXT_SOURCE" "$DEMO_DIR/"

    # 复制模型头文件
    if [ -f "ConvNextModel.hpp" ]; then
        echo "Copying ConvNext model header..."
        cp "ConvNextModel.hpp" "$DEMO_DIR/"
    fi



    # 复制ConvNext DataLoader头文件
    if [ -f "ConvNextDataLoader.hpp" ]; then
        echo "Copying ConvNext DataLoader header..."
        cp "ConvNextDataLoader.hpp" "$DEMO_DIR/"
    fi

    # 验证复制是否成功
    if [ -f "$DEMO_DIR/ConvNextTrainingDemo.cpp" ]; then
        echo "✅ Training code synchronized successfully"
        echo "   Source: $(pwd)/$CONVNEXT_SOURCE"
        echo "   Target: $DEMO_DIR/ConvNextTrainingDemo.cpp"
    else
        echo "❌ Error: Failed to copy training code"
        exit 1
    fi
else
    echo "❌ Error: ConvNextTrainingDemo.cpp not found in current directory"
    echo "Please ensure the training code exists: $(pwd)/$CONVNEXT_SOURCE"
    exit 1
fi

# 创建构建目录并切换到构建目录
echo ""
echo "=== Preparing Build Directory ==="
mkdir -p $BUILD_DIR
echo "Changing to build directory: $BUILD_DIR"
cd $BUILD_DIR

echo ""
echo "=== Configuring CMake for ConvNext Android Training ==="

# 配置CMake - 启用训练和GPU支持
cmake ../../../ \
    -DCMAKE_TOOLCHAIN_FILE=$ANDROID_NDK/build/cmake/android.toolchain.cmake \
    -DCMAKE_BUILD_TYPE=Release \
    -DANDROID_ABI="arm64-v8a" \
    -DANDROID_STL=c++_static \
    -DMNN_USE_LOGCAT=false \
    -DMNN_BUILD_BENCHMARK=ON \
    -DMNN_USE_SSE=OFF \
    -DMNN_BUILD_TEST=ON \
    -DMNN_BUILD_TRAIN=ON \
    -DMNN_BUILD_TRAIN_MINI=ON \
    -DMNN_OPENCL=ON \
    -DMNN_ARM82=ON \
    -DANDROID_NATIVE_API_LEVEL=android-21 \
    -DMNN_BUILD_FOR_ANDROID_COMMAND=true \
    -DNATIVE_LIBRARY_OUTPUT=. -DNATIVE_INCLUDE_OUTPUT=. $*

if [ $? -ne 0 ]; then
    echo "Error: CMake configuration failed"
    exit 1
fi

echo ""
echo "=== Building ConvNext Training Program ==="

# 编译训练程序
make runTrainDemo.out -j4

if [ $? -ne 0 ]; then
    echo "Error: Build failed"
    exit 1
fi

echo ""
echo "=== Build Summary ==="
echo "✅ ConvNext training program built successfully!"
echo ""
echo "Source code used:"
echo "  - ConvNext training code: $CONVNEXT_DIR/ConvNextTrainingDemo.cpp"
echo "  - ConvNext model header: $CONVNEXT_DIR/ConvNextModel.hpp"
echo "  - Compiled from: $DEMO_DIR/ConvNextTrainingDemo.cpp"
echo ""
echo "Generated files in $BUILD_DIR:"
echo "  - runTrainDemo.out (Training executable)"
echo "  - libMNN.so (MNN core library)"
echo "  - libMNN_Express.so (MNN expression library)"
echo "  - libMNN_CL.so (OpenCL GPU library)"
echo "  - libMNNTrain.so (Training library)"
echo "  - libMNNTrainUtils.so (Training utilities)"
echo ""
echo "Features enabled:"
echo "  ✅ GPU Training (OpenCL)"
echo "  ✅ FP32 Precision"
echo "  ✅ ARM64 Optimization"
echo "  ✅ ConvNext Mobile Training"
echo ""
echo "Next steps:"
echo "  1. Run: ./deploy_convnext_android.sh to deploy to device"
echo "  2. Or manually push files to Android device"

cd - > /dev/null
echo ""
echo "Build completed successfully! 🎉"
