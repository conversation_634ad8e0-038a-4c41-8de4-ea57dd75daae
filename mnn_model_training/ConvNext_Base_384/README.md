# ConvNext Mobile Training

ConvNext模型在Android设备上的GPU训练实现。

## 📁 目录结构

```
ConvNext/
├── README.md                           # 本文档
├── build_convnext_android.sh          # Android编译脚本
├── deploy_convnext_android.sh         # Android部署脚本
├── sync_training_code.sh              # 训练代码同步脚本
├── ConvNextMobileDemo.cpp             # ConvNext训练代码（本地副本）
├── ConvNextDataLoader.hpp             # 数据加载器
├── create_real_test_images.py         # 测试数据生成脚本
├── convnext_base_384_batch1_imgsz384.mnn  # ConvNext推理模型
├── convnext_train_config.json         # 训练配置文件
└── convnext_revert_mse.json           # 模型转换配置

编译时使用的代码位置：
└── ../../tools/train/source/demo/ConvNextMobileDemo.cpp  # 编译时的实际位置
```

## 🚀 快速开始

### 1. 生成测试数据

```bash
cd /path/to/mnn/model_training/ConvNext
python3 create_real_test_images.py
```

### 2. 编译ConvNext训练程序

```bash
chmod +x build_convnext_android.sh
./build_convnext_android.sh
```

### 3. 部署到Android设备

```bash
chmod +x deploy_convnext_android.sh
./deploy_convnext_android.sh
```

### 4. 在Android设备上运行训练

```bash
# 连接到设备
adb shell

# 进入训练目录
cd /data/local/tmp/convnext_training

# 运行ConvNext GPU训练
LD_LIBRARY_PATH=. ./runTrainDemo.out ConvNextMobileTraining ./real_test_data/train/ ./real_test_data/train.txt
```

## 🔧 技术特性

### GPU训练支持
- ✅ **OpenCL GPU后端**：使用手机GPU进行训练加速
- ✅ **FP32精度**：高精度浮点运算确保训练质量
- ✅ **ARM优化**：支持ARM64架构和高级指令集

### 训练配置
- **批次大小**：1（适合移动设备内存限制）
- **学习率**：0.0001（适合移动端训练）
- **优化器**：ADAM
- **损失函数**：MSE（均方误差）

### 设备要求
- Android 5.0+ (API 21+)
- ARM64架构处理器
- 支持OpenCL的GPU
- 至少4GB RAM

## 📊 性能表现

### 测试设备：OPPO CPH2449
- **GPU特性**：i8sdot:1, fp16:1, i8mm:1
- **训练速度**：~13秒/epoch（10个样本）
- **内存使用**：稳定，无内存泄漏
- **模型大小**：1GB训练模型

### 训练结果示例
```
=== ConvNext Mobile Training Demo ===
Using GPU (OpenCL) backend for training with FP32 precision

--- Epoch 1/3 ---
Iteration 0/10, Loss: 1115.5, LR: 0.0001
Epoch 1 completed in 13 seconds. Average loss: 1115.5

--- Epoch 2/3 ---
Iteration 0/10, Loss: 1070.85, LR: 9e-05
Epoch 2 completed in 13 seconds. Average loss: 972.378

--- Epoch 3/3 ---
Iteration 0/10, Loss: 880.937, LR: 8.1e-05
Epoch 3 completed in 13 seconds. Average loss: 878.105

=== Training Completed ===
Final model saved: convnext_mobile_final.mnn
```

## 🛠️ 开发说明

### 代码文件说明

1. **ConvNextMobileDemo.cpp**
   - 主要的ConvNext训练实现
   - 集成到MNN训练框架中
   - 支持GPU训练和FP32精度

2. **ConvNextGPUTrain.cpp**
   - 简化版本的GPU训练代码
   - 用于测试和验证GPU功能
   - 更直接的API调用

### 编译选项

```cmake
-DMNN_BUILD_TRAIN=ON          # 启用训练功能
-DMNN_BUILD_TRAIN_MINI=ON     # 启用轻量训练
-DMNN_OPENCL=ON               # 启用OpenCL GPU支持
-DMNN_ARM82=ON                # 启用ARM高级指令集
```

### 关键配置

```cpp
// GPU配置
MNNForwardType backend = MNN_FORWARD_OPENCL;
BackendConfig config;
config.precision = BackendConfig::Precision_High; // FP32
config.power = BackendConfig::Power_High;         // 高性能模式
```

## 🔍 故障排除

### 常见问题

1. **Segmentation Fault**
   - 检查图像数据格式是否正确（需要真实JPEG文件）
   - 确保模型文件路径正确
   - 验证设备内存是否充足

2. **GPU初始化失败**
   - 检查设备是否支持OpenCL
   - 确认libMNN_CL.so已正确部署
   - 尝试降低模型复杂度

3. **编译错误**
   - 检查Android NDK路径设置
   - 确认CMake版本兼容性
   - 验证依赖库是否完整

### 调试技巧

```bash
# 检查设备GPU信息
adb shell "cat /proc/cpuinfo | grep -E 'processor|model name'"

# 检查内存使用
adb shell "cat /proc/meminfo | grep MemAvailable"

# 查看训练日志
adb shell "cd /data/local/tmp/convnext_training && LD_LIBRARY_PATH=. ./runTrainDemo.out ConvNextMobileTraining ./real_test_data/train/ ./real_test_data/train.txt 2>&1 | tee training.log"
```

## 📈 未来改进

- [ ] 支持更多模型架构（ResNet、EfficientNet等）
- [ ] 实现量化感知训练
- [ ] 添加训练进度可视化
- [ ] 支持联邦学习
- [ ] 优化内存使用和训练速度

## 📄 许可证

本项目遵循MNN项目的许可证条款。
