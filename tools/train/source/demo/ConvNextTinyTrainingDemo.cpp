//
//  ConvNextTinyTrainingDemo.cpp
//  MNN
//
//  ConvNext Tiny训练演示 - 使用精确ConvNext Tiny模型和DataLoader
//

#include <MNN/expr/Executor.hpp>
#include <MNN/expr/Optimizer.hpp>
#include <MNN/expr/Module.hpp>
#include <MNN/MNNForwardType.h>
#include <cmath>
#include <iostream>
#include <vector>
#include <string>
#include <sstream>
#include <memory>
#include <chrono>
#include <algorithm>
#include <thread>
#include "DemoUnit.hpp"
#include "ConvNextTinyModel.hpp"
#include "ConvNextTinyDataLoader.hpp"
#include "SGD.hpp"
#include "Loss.hpp"
#include "LearningRateScheduler.hpp"
#include "RandomGenerator.hpp"
#include "Transformer.hpp"
#include "ADAM.hpp"

using namespace MNN::Express;
using namespace MNN::Train;
using namespace std;

// 常量定义
const int INPUT_SIZE = 224;   // ConvNext Tiny输入尺寸
const int NUM_CLASSES = 1000; // ImageNet分类数

class ConvNextTinyMobileTraining : public DemoUnit
{
public:
    virtual int run(int argc, const char *argv[]) override
    {
        if (argc < 3)
        {
            cout << "usage: ./runTrainDemo.out ConvNextTinyMobileTraining path/to/train/images/ path/to/train/image/txt [backend_type] [batch_size]" << endl;
            cout << "backend_type: 0=CPU, 3=GPU(OpenCL)" << endl;
            cout << "batch_size: 1,4,8,16,32,64 (default: 1)" << endl;
            return 0;
        }

        // 默认使用GPU训练，可通过参数指定
        MNNForwardType backend = MNN_FORWARD_OPENCL;
        if (argc >= 4)
        {
            int backendType = atoi(argv[3]);
            if (backendType == 0)
            {
                backend = MNN_FORWARD_CPU;
            }
        }

        // 解析batch size参数
        int batchSize = 1; // 默认batch size
        if (argc >= 5)
        {
            int inputBatchSize = atoi(argv[4]);
            // 验证batch size是否在支持的范围内
            vector<int> supportedBatchSizes = {1, 4, 8, 16, 32, 64};
            bool isSupported = false;
            for (int size : supportedBatchSizes)
            {
                if (inputBatchSize == size)
                {
                    batchSize = inputBatchSize;
                    isSupported = true;
                    break;
                }
            }
            if (!isSupported)
            {
                cout << "Warning: Unsupported batch size " << inputBatchSize
                     << ". Using default batch size 1." << endl;
                cout << "Supported batch sizes: 1, 4, 8, 16, 32, 64" << endl;
            }
        }

        // 设置随机种子
        RandomGenerator::generator(42);

        string trainImagesPath = argv[1];
        string trainLabelFile = argv[2];

        cout << "\n=== ConvNext Tiny Mobile Training Demo ===" << endl;
        cout << "Backend: " << (backend == MNN_FORWARD_OPENCL ? "GPU (OpenCL)" : "CPU") << endl;
        cout << "Batch Size: " << batchSize << endl;
        cout << "Input Size: " << INPUT_SIZE << "x" << INPUT_SIZE << endl;
        cout << "Num Classes: " << NUM_CLASSES << endl;
        cout << "=========================================" << endl;

        // 创建ConvNext Tiny模型
        cout << "\nCreating ConvNext Tiny model..." << endl;
        auto model = shared_ptr<Module>(new ConvNextTinyModel());
        cout << "ConvNext Tiny model created successfully!" << endl;

        // 配置GPU后端 - FP32精度
        MNN::ScheduleConfig scheduleConfig;
        scheduleConfig.type = backend;
        scheduleConfig.numThread = 1;

        MNN::BackendConfig backendConfig;
        backendConfig.precision = MNN::BackendConfig::Precision_High; // FP32精度
        backendConfig.power = MNN::BackendConfig::Power_High;         // 高性能模式

        scheduleConfig.backendConfig = &backendConfig;

        // 获取执行器用于垃圾回收
        auto exe = Executor::getGlobalExecutor();

        // 创建数据集和数据加载器
        cout << "\nCreating dataset and dataloader..." << endl;
        auto dataset = shared_ptr<ConvNextTinyImageDataset>(new ConvNextTinyImageDataset(trainImagesPath, trainLabelFile, INPUT_SIZE));
        auto dataLoader = shared_ptr<ConvNextTinyDataLoader>(new ConvNextTinyDataLoader(dataset, batchSize, true, INPUT_SIZE));

        cout << "Dataset size: " << dataset->size() << endl;
        cout << "DataLoader created successfully!" << endl;

        // 创建优化器 - ADAM
        auto optimizer = shared_ptr<SGD>(new ADAM(model));
        const float constantLearningRate = 0.001f;
        optimizer->setLearningRate(constantLearningRate);

        cout << "\nBackend: " << (backend == MNN_FORWARD_OPENCL ? "GPU (OpenCL)" : "CPU") << endl;
        cout << "Model: ConvNext Tiny for image classification" << endl;
        cout << "Loss: MSE Loss" << endl;
        cout << "Precision: FP32" << endl;
        cout << "Batch Size: " << batchSize << endl;
        cout << "Input Size: " << INPUT_SIZE << "x" << INPUT_SIZE << endl;
        cout << "Num Classes: " << NUM_CLASSES << endl;

        // 训练循环 - 训练2个epoch，每个epoch 10个iteration
        const int num_epochs = 2;
        const int FIXED_ITERATIONS_PER_EPOCH = 10;

        cout << "\n=== ConvNext Tiny Image Classification Training Started ===" << endl;
        cout << "Fixed iterations per epoch: " << FIXED_ITERATIONS_PER_EPOCH << endl;

        for (int epoch = 0; epoch < num_epochs; ++epoch)
        {
            model->clearCache();
            cout << "\n--- Epoch " << (epoch + 1) << "/" << num_epochs << " ---" << endl;

            float epochLoss = 0.0f;

            for (int i = 0; i < FIXED_ITERATIONS_PER_EPOCH; i++)
            {
                // 开始计时
                auto iterStart = std::chrono::high_resolution_clock::now();

                // 从DataLoader获取batch数据
                auto batchData = dataLoader->next();
                auto input = batchData.first;   // [batch_size, 3, 224, 224]
                auto labels = batchData.second; // [batch_size] - 整数标签

                // 前向传播
                auto output = model->onForward({input})[0];

                // 将整数标签转换为one-hot编码
                auto targetOneHot = _OneHot(_Cast(labels, halide_type_of<int>()), _Scalar<int>(NUM_CLASSES), _Scalar<float>(1.0f), _Scalar<float>(0.0f));

                // 计算MSE损失 - 使用MNN的_MSE函数
                auto lossValue = _MSE(output, targetOneHot);
                float currentLoss = lossValue->readMap<float>()[0];
                epochLoss += currentLoss;

                // 反向传播
                optimizer->step(lossValue);

                // 结束计时
                auto iterEnd = std::chrono::high_resolution_clock::now();
                auto iterDuration = std::chrono::duration_cast<std::chrono::milliseconds>(iterEnd - iterStart);

                // YOLOv10格式输出
                cout << "train iteration: " << optimizer->currentStep();
                cout << " batch_size: " << batchSize;
                cout << " mse_loss: " << std::fixed << std::setprecision(3) << currentLoss;
                cout << " lr: " << constantLearningRate;
                cout << " time: " << iterDuration.count() << "ms" << endl;

                // 垃圾回收
                exe->gc(Executor::FULL);
            }

            float avgLoss = epochLoss / FIXED_ITERATIONS_PER_EPOCH;
            cout << "Epoch " << (epoch + 1) << " completed. Average MSE loss: " << avgLoss << endl;
        }

        cout << "\n=== ConvNext Tiny Training Completed Successfully ===" << endl;
        return 0;
    }
};

DemoUnitSetRegister(ConvNextTinyMobileTraining, "ConvNextTinyMobileTraining");
