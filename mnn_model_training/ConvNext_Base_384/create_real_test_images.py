#!/usr/bin/env python3
"""
创建真正的JPEG测试图像用于MNN训练

这个脚本为ConvNext移动端训练创建测试数据集。
生成的图像是384x384的RGB JPEG格式，适合ConvNext模型训练。
"""

from PIL import Image
import numpy as np
import os
import argparse
import sys

def create_real_test_images(num_classes, num_images, output_dir="real_test_data"):
    """
    创建真正的JPEG测试图像

    Args:
        num_classes (int): 要创建的类别数量
        num_images (int): 每个类别的图像数量
        output_dir (str): 输出目录路径
    """

    print(f"Creating real JPEG test images...")
    print(f"Classes: {num_classes}, Images per class: {num_images}")
    print(f"Total images: {num_classes * num_images}")

    # 创建根目录
    train_dir = os.path.join(output_dir, "train")
    os.makedirs(train_dir, exist_ok=True)

    train_list = []

    # 定义颜色方案
    color_schemes = [
        ([100, 255], [30, 80], [30, 80]),    # 红色渐变
        ([30, 80], [30, 80], [100, 255]),    # 蓝色渐变
        ([30, 80], [100, 255], [30, 80]),    # 绿色渐变
        ([100, 255], [100, 255], [30, 80]),  # 黄色渐变
        ([100, 255], [30, 80], [100, 255]),  # 紫色渐变
        ([30, 80], [100, 255], [100, 255]),  # 青色渐变
        ([255, 100], [100, 255], [30, 80]),  # 橙色渐变
        ([100, 200], [100, 200], [100, 200]) # 灰色渐变
    ]

    # 为每个类别创建图像
    for class_id in range(num_classes):
        class_dir = os.path.join(train_dir, f"class{class_id}")
        os.makedirs(class_dir, exist_ok=True)

        # 选择颜色方案（循环使用）
        color_scheme = color_schemes[class_id % len(color_schemes)]

        for img_id in range(num_images):
            # 创建384x384的RGB图像（ConvNext标准输入尺寸）
            img_array = np.zeros((384, 384, 3), dtype=np.uint8)

            # 应用颜色方案
            for channel in range(3):
                min_val, max_val = color_scheme[channel]
                # 创建渐变效果
                gradient = np.linspace(min_val, max_val, 384)
                img_array[:, :, channel] = gradient.reshape(1, -1)

            # 添加一些随机噪声和纹理使图像更真实
            noise = np.random.randint(-15, 15, (384, 384, 3))
            img_array = np.clip(img_array.astype(np.int16) + noise, 0, 255).astype(np.uint8)

            # 添加一些几何图案
            if img_id % 2 == 0:
                # 添加对角线纹理
                for i in range(0, 384, 20):
                    img_array[i:i+2, :, :] = np.minimum(img_array[i:i+2, :, :] + 30, 255)
            else:
                # 添加圆形纹理
                center = (192, 192)
                y, x = np.ogrid[:384, :384]
                mask = (x - center[0])**2 + (y - center[1])**2 < 50**2
                img_array[mask] = np.minimum(img_array[mask] + 20, 255)

            # 创建PIL图像并保存为JPEG
            img = Image.fromarray(img_array)
            img_path = os.path.join(class_dir, f"img_{img_id:03d}.jpg")
            img.save(img_path, "JPEG", quality=85)

            # 添加到训练列表
            train_list.append(f"class{class_id}/img_{img_id:03d}.jpg {class_id}")

            if (img_id + 1) % 5 == 0 or img_id == num_images - 1:
                print(f"  Class {class_id}: Created {img_id + 1}/{num_images} images")

    # 保存训练列表文件
    train_file = os.path.join(output_dir, "train.txt")
    with open(train_file, "w") as f:
        for line in train_list:
            f.write(line + "\n")

    print(f"\n✅ Successfully created {len(train_list)} real JPEG training images")
    print(f"📁 Test data structure:")
    print(f"{output_dir}/")
    print("├── train/")
    for i in range(num_classes):
        color_name = ["red", "blue", "green", "yellow", "purple", "cyan", "orange", "gray"][i % 8]
        if i == num_classes - 1:
            print(f"│   └── class{i}/ ({num_images} {color_name} gradient images)")
        else:
            print(f"│   ├── class{i}/ ({num_images} {color_name} gradient images)")
    print("└── train.txt (training list)")
    print(f"\n📊 Dataset summary:")
    print(f"   • Classes: {num_classes}")
    print(f"   • Images per class: {num_images}")
    print(f"   • Total images: {len(train_list)}")
    print(f"   • Image format: 384x384 RGB JPEG")
    print(f"   • Training list: {train_file}")
    print(f"\n🎯 Ready for ConvNext mobile training!")

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="创建ConvNext移动端训练的测试图像数据集",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s --num_classes 5 --num_images 20
  %(prog)s -c 10 -n 50
  %(prog)s --help

生成的数据集结构:
  real_test_data/
  ├── train/
  │   ├── class0/ (红色渐变图像)
  │   ├── class1/ (蓝色渐变图像)
  │   ├── class2/ (绿色渐变图像)
  │   └── ...
  └── train.txt (训练列表文件)

注意:
  • 图像格式: 384x384 RGB JPEG
  • 适用于ConvNext模型训练
  • 每个类别使用不同的颜色渐变和纹理
        """
    )

    parser.add_argument(
        '--num_classes', '-c',
        type=int,
        default=5,
        help='要创建的类别数量 (默认: 5)'
    )

    parser.add_argument(
        '--num_images', '-n',
        type=int,
        default=20,
        help='每个类别的图像数量 (默认: 20)'
    )

    parser.add_argument(
        '--output_dir', '-o',
        type=str,
        default='real_test_data',
        help='输出目录 (默认: real_test_data)'
    )

    return parser.parse_args()

def validate_arguments(args):
    """验证命令行参数"""
    if args.num_classes <= 0:
        print("❌ 错误: num_classes 必须大于 0")
        sys.exit(1)

    if args.num_images <= 0:
        print("❌ 错误: num_images 必须大于 0")
        sys.exit(1)

    if args.num_classes > 1000:
        print("⚠️  警告: 类别数量很大，可能需要较长时间生成")

    if args.num_images > 100:
        print("⚠️  警告: 每类图像数量很大，可能需要较长时间生成")

    total_images = args.num_classes * args.num_images
    if total_images > 10000:
        print(f"⚠️  警告: 总图像数量 {total_images} 很大，确保有足够的磁盘空间")

if __name__ == "__main__":
    print("🖼️  ConvNext Mobile Training Dataset Creator")
    print("=" * 50)

    # 解析命令行参数
    args = parse_arguments()

    # 验证参数
    validate_arguments(args)

    # 显示配置信息
    print(f"📋 配置信息:")
    print(f"   类别数量: {args.num_classes}")
    print(f"   每类图像数: {args.num_images}")
    print(f"   总图像数: {args.num_classes * args.num_images}")
    print(f"   输出目录: {args.output_dir}")
    print()

    try:
        # 创建测试图像
        create_real_test_images(args.num_classes, args.num_images, args.output_dir)
        print("\n🎉 数据集创建完成!")

    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 创建数据集时发生错误: {e}")
        sys.exit(1)
