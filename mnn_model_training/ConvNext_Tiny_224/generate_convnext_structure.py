#!/usr/bin/env python3
"""
Generate ConvNext Tiny model structure information for MNN training implementation
"""

from transformers import ConvNextImageProcessor, ConvNextForImageClassification
import torch
import sys
import os

def generate_convnext_tiny_structure():
    """Generate complete ConvNext Tiny model structure information"""
    
    # Load model and processor
    model_name = 'facebook/convnext-tiny-224'
    print(f'Loading ConvNext Tiny model: {model_name}')
    
    try:
        model = ConvNextForImageClassification.from_pretrained(model_name)
        model.eval()
        print('ConvNext Tiny model loaded successfully!')
    except Exception as e:
        print(f'Error loading model: {e}')
        return
    
    # Create output file
    output_file = 'convnext_tiny_structure.txt'
    
    with open(output_file, 'w') as f:
        f.write('='*80 + '\n')
        f.write('CONVNEXT TINY MODEL COMPLETE STRUCTURE\n')
        f.write('='*80 + '\n')
        f.write(str(model) + '\n\n')
        
        f.write('='*80 + '\n')
        f.write('CONVNEXT TINY MODEL CONFIG\n')
        f.write('='*80 + '\n')
        f.write(str(model.config) + '\n\n')
        
        f.write('='*80 + '\n')
        f.write('CONVNEXT TINY MODEL NAMED MODULES\n')
        f.write('='*80 + '\n')
        for name, module in model.named_modules():
            f.write(f'{name}: {type(module).__name__}\n')
        f.write('\n')
        
        f.write('='*80 + '\n')
        f.write('CONVNEXT TINY MODEL PARAMETERS\n')
        f.write('='*80 + '\n')
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        f.write(f'Total parameters: {total_params:,}\n')
        f.write(f'Trainable parameters: {trainable_params:,}\n\n')
        
        f.write('='*80 + '\n')
        f.write('CONVNEXT TINY MODEL INPUT/OUTPUT INFO\n')
        f.write('='*80 + '\n')
        f.write(f'Input size: 3x224x224\n')
        f.write(f'Number of classes: {model.config.num_labels}\n')
        f.write(f'Hidden sizes: {model.config.hidden_sizes}\n')
        f.write(f'Depths: {model.config.depths}\n')
        f.write(f'Drop path rate: {model.config.drop_path_rate}\n')
        f.write(f'Layer scale init value: {model.config.layer_scale_init_value}\n\n')
        
        f.write('='*80 + '\n')
        f.write('CONVNEXT TINY LAYER DETAILS\n')
        f.write('='*80 + '\n')
        
        # Print detailed layer information
        for name, module in model.named_modules():
            if hasattr(module, 'weight') and module.weight is not None:
                f.write(f'{name}:\n')
                f.write(f'  Type: {type(module).__name__}\n')
                f.write(f'  Weight shape: {module.weight.shape}\n')
                if hasattr(module, 'bias') and module.bias is not None:
                    f.write(f'  Bias shape: {module.bias.shape}\n')
                else:
                    f.write(f'  Bias: None\n')
                f.write('\n')
    
    print(f'ConvNext Tiny structure saved to: {output_file}')
    
    # Also print key information to console
    print('\n' + '='*60)
    print('KEY CONVNEXT TINY MODEL INFORMATION')
    print('='*60)
    print(f'Model: {model_name}')
    print(f'Total parameters: {total_params:,}')
    print(f'Input size: 3x224x224')
    print(f'Number of classes: {model.config.num_labels}')
    print(f'Hidden sizes: {model.config.hidden_sizes}')
    print(f'Depths: {model.config.depths}')
    print(f'Drop path rate: {model.config.drop_path_rate}')

if __name__ == '__main__':
    generate_convnext_tiny_structure()
