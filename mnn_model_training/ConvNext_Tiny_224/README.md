# ConvNext Tiny Mobile Training

ConvNext Tiny模型在Android设备上的GPU训练实现 - 轻量级版本，专为移动设备优化。

## 📁 目录结构

```
ConvNext_Tiny_224/
├── README.md                                    # 本文档
├── 0_generate_training_model.sh                # 训练模型生成脚本
├── 1_build_convnext_tiny_android.sh            # Android编译脚本
├── 2_deploy_convnext_tiny_android.sh           # Android部署脚本
├── 3_convnext_tiny_model_training.sh           # 训练执行脚本
├── ConvNextTinyTrainingDemo.cpp                # ConvNext Tiny训练代码
├── ConvNextTinyModel.hpp                       # ConvNext Tiny模型定义
├── ConvNextTinyDataLoader.hpp                  # 数据加载器
├── create_real_test_images.py                  # 测试数据生成脚本
├── generate_convnext_structure.py              # 模型结构生成脚本
├── convnext_tiny_structure.txt                 # 模型结构信息
├── convnext_tiny_224_batch1_imgsz224.mnn      # ConvNext Tiny推理模型
└── convnext_tiny_train_mse.mnn                # ConvNext Tiny训练模型

编译时使用的代码位置：
└── ../../tools/train/source/demo/ConvNextTinyTrainingDemo.cpp  # 编译时的实际位置
```

## 🚀 快速开始

### 1. 生成模型结构信息

```bash
cd /path/to/mnn/mnn_model_training/ConvNext_Tiny_224
python3 generate_convnext_structure.py
```

### 2. 生成测试数据

```bash
python3 create_real_test_images.py --num_classes 5 --num_images 20
```

### 3. 生成训练模型

```bash
chmod +x 0_generate_training_model.sh
./0_generate_training_model.sh
```

### 4. 编译ConvNext Tiny训练程序

```bash
chmod +x 1_build_convnext_tiny_android.sh
./1_build_convnext_tiny_android.sh
```

### 5. 部署到Android设备

```bash
chmod +x 2_deploy_convnext_tiny_android.sh
./2_deploy_convnext_tiny_android.sh
```

### 6. 在Android设备上运行训练

```bash
# 使用训练脚本（推荐）
chmod +x 3_convnext_tiny_model_training.sh
./3_convnext_tiny_model_training.sh

# 或者手动执行
adb shell "cd /data/local/tmp/convnext_tiny_training && LD_LIBRARY_PATH=. ./runTrainDemo.out ConvNextTinyMobileTraining ./real_test_data/train/ ./real_test_data/train.txt 3 4"
```

## 🔧 技术特性

### ConvNext Tiny架构
- ✅ **轻量级设计**：28,589,128参数（vs ConvNext Base的88.6M）
- ✅ **移动优化**：224x224输入尺寸（vs 384x384）
- ✅ **高效结构**：Hidden sizes [96, 192, 384, 768]
- ✅ **平衡深度**：Depths [3, 3, 9, 3]

### GPU训练支持
- ✅ **OpenCL GPU后端**：使用手机GPU进行训练加速
- ✅ **FP32精度**：高精度浮点运算确保训练质量
- ✅ **ARM优化**：支持ARM64架构和高级指令集
- ✅ **批次训练**：支持多种批次大小 (1, 4, 8, 16, 32, 64)

### 训练配置
- **输入尺寸**：224x224x3（移动设备友好）
- **批次大小**：1-64（可配置）
- **学习率**：0.001（ADAM优化器）
- **损失函数**：MSE（均方误差）
- **训练轮数**：2 epochs，每轮10次迭代

### 设备要求
- Android 5.0+ (API 21+)
- ARM64架构处理器
- 支持OpenCL的GPU
- 至少3GB RAM（相比Base版本降低内存需求）

## 📊 性能优势

### ConvNext Tiny vs ConvNext Base
| 特性 | ConvNext Tiny | ConvNext Base | 优势 |
|------|---------------|---------------|------|
| 参数量 | 28.6M | 88.6M | 68%减少 |
| 输入尺寸 | 224x224 | 384x384 | 43%减少 |
| 内存需求 | ~3GB | ~4GB+ | 25%减少 |
| 训练速度 | 更快 | 较慢 | 显著提升 |
| 功耗 | 更低 | 较高 | 移动友好 |

### 测试设备性能表现
- **GPU特性**：i8sdot:1, fp16:1, i8mm:1
- **训练速度**：~8-10秒/epoch（相比Base版本的13秒）
- **内存使用**：稳定，更低内存占用
- **模型大小**：~500MB训练模型（相比Base的1GB）

### 训练结果示例
```
=== ConvNext Tiny Mobile Training Demo ===
Using GPU (OpenCL) backend for training with FP32 precision
Model: ConvNext Tiny (28.6M parameters, 224x224 input)

--- Epoch 1/2 ---
Iteration 0/10, Loss: 892.3, LR: 0.001
Epoch 1 completed in 9 seconds. Average loss: 892.3

--- Epoch 2/2 ---
Iteration 0/10, Loss: 756.8, LR: 0.001
Epoch 2 completed in 8 seconds. Average loss: 678.2

=== Training Completed ===
Final model saved: convnext_tiny_mobile_final.mnn
```

## 🛠️ 开发说明

### 代码文件说明

1. **ConvNextTinyTrainingDemo.cpp**
   - 主要的ConvNext Tiny训练实现
   - 集成到MNN训练框架中
   - 支持GPU训练和FP32精度
   - 优化的批次处理和内存管理

2. **ConvNextTinyModel.hpp**
   - ConvNext Tiny模型架构定义
   - 包含LayerNorm、ConvNext层和完整模型
   - 针对224x224输入优化

3. **ConvNextTinyDataLoader.hpp**
   - 轻量级数据加载器
   - 支持224x224图像处理
   - 批次数据生成和管理

### 编译选项

```cmake
-DMNN_BUILD_TRAIN=ON          # 启用训练功能
-DMNN_BUILD_TRAIN_MINI=ON     # 启用轻量训练
-DMNN_OPENCL=ON               # 启用OpenCL GPU支持
-DMNN_ARM82=ON                # 启用ARM高级指令集
```

### 关键配置

```cpp
// GPU配置
MNNForwardType backend = MNN_FORWARD_OPENCL;
BackendConfig config;
config.precision = BackendConfig::Precision_High; // FP32
config.power = BackendConfig::Power_High;         // 高性能模式

// ConvNext Tiny特定配置
const int CONVNEXT_TINY_INPUT_SIZE = 224;
const std::vector<int> CONVNEXT_TINY_HIDDEN_SIZES = {96, 192, 384, 768};
const std::vector<int> CONVNEXT_TINY_DEPTHS = {3, 3, 9, 3};
```

## 🔍 故障排除

### 常见问题

1. **Segmentation Fault**
   - 检查图像数据格式是否正确（需要真实JPEG文件）
   - 确保模型文件路径正确
   - 验证设备内存是否充足（ConvNext Tiny需求更低）

2. **GPU初始化失败**
   - 检查设备是否支持OpenCL
   - 确认libMNN_CL.so已正确部署
   - ConvNext Tiny对GPU要求更低，兼容性更好

3. **编译错误**
   - 检查Android NDK路径设置
   - 确认CMake版本兼容性
   - 验证依赖库是否完整

4. **内存不足**
   - ConvNext Tiny相比Base版本内存需求降低68%
   - 如仍有问题，尝试减小批次大小
   - 检查设备可用内存

### 调试技巧

```bash
# 检查设备GPU信息
adb shell "cat /proc/cpuinfo | grep -E 'processor|model name'"

# 检查内存使用
adb shell "cat /proc/meminfo | grep MemAvailable"

# 查看训练日志
adb shell "cd /data/local/tmp/convnext_tiny_training && LD_LIBRARY_PATH=. ./runTrainDemo.out ConvNextTinyMobileTraining ./real_test_data/train/ ./real_test_data/train.txt 3 4 2>&1 | tee training.log"

# 测试不同批次大小
./3_convnext_tiny_model_training.sh 1 4 8  # 测试小批次
./3_convnext_tiny_model_training.sh 16 32  # 测试大批次
```

## 📈 未来改进

- [ ] 支持量化训练（INT8）进一步减少内存和计算
- [ ] 实现知识蒸馏从ConvNext Base到Tiny
- [ ] 添加更多移动优化（如深度可分离卷积）
- [ ] 支持动态批次大小
- [ ] 实现模型剪枝和压缩
- [ ] 添加训练进度可视化

## 🎯 使用建议

### 最佳实践
1. **批次大小选择**：建议从小批次（1-4）开始测试
2. **内存管理**：ConvNext Tiny已优化，但仍需监控内存使用
3. **训练策略**：可以先用Tiny版本快速验证，再考虑Base版本
4. **设备选择**：ConvNext Tiny对中低端设备更友好

### 性能调优
- 使用GPU训练（OpenCL后端）获得最佳性能
- 根据设备内存调整批次大小
- 监控训练过程中的内存和GPU使用率
- 利用ARM优化指令集提升计算效率

## 📄 许可证

本项目遵循MNN项目的许可证条款。
