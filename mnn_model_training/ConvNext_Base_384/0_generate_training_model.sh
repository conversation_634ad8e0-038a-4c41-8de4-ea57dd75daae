#!/bin/bash

# ConvNext Training Model Generation Script
# 从推理模型生成完整的训练模型

set -e

echo "=== ConvNext Training Model Generation ==="

# 文件定义
INFERENCE_MODEL="convnext_base_384_batch1_imgsz384.mnn"
OUTPUT_TRAINING_MODEL="convnext_train_mse.mnn"
TRANSFORMER_TOOL="../../build/transformer"

echo "Process: Inference Model → Complete Training Model"
echo "Input:  $INFERENCE_MODEL"
echo "Output: $OUTPUT_TRAINING_MODEL"
echo ""

# 检查工具
if [ ! -f "$TRANSFORMER_TOOL" ]; then
    echo "❌ Error: Transformer tool not found: $TRANSFORMER_TOOL"
    echo "Please build MNN first: cd ../../build && make -j4"
    exit 1
fi

# 检查推理模型
if [ ! -f "$INFERENCE_MODEL" ]; then
    echo "❌ Error: Inference model not found: $INFERENCE_MODEL"
    exit 1
fi

echo "=== Step 1: Creating Minimal Training Configuration ==="
echo "Using minimal config for complete model conversion..."

# 创建最简单的训练配置（这是成功的关键）
echo '{"Train": true}' > temp_config.json

echo "✅ Created minimal training configuration"

echo ""
echo "=== Step 2: Converting to Complete Training Model ==="
echo "Running transformer with minimal config..."

# 备份现有模型
if [ -f "$OUTPUT_TRAINING_MODEL" ]; then
    echo "Creating backup of existing training model..."
    cp "$OUTPUT_TRAINING_MODEL" "${OUTPUT_TRAINING_MODEL}.backup"
fi

# 执行模型转换
echo "Running: $TRANSFORMER_TOOL $INFERENCE_MODEL $OUTPUT_TRAINING_MODEL temp_config.json"
$TRANSFORMER_TOOL $INFERENCE_MODEL $OUTPUT_TRAINING_MODEL temp_config.json

echo ""
echo "=== Step 3: Verification ==="
if [ -f "$OUTPUT_TRAINING_MODEL" ]; then
    MODEL_SIZE=$(ls -lh "$OUTPUT_TRAINING_MODEL" | awk '{print $5}')
    echo "✅ Training model generated successfully!"
    echo "   File: $OUTPUT_TRAINING_MODEL"
    echo "   Size: $MODEL_SIZE"

    # 验证模型大小（应该是几百MB，不是几KB）
    SIZE_BYTES=$(stat -c%s "$OUTPUT_TRAINING_MODEL")
    if [ $SIZE_BYTES -gt 100000000 ]; then  # > 100MB
        echo "✅ Model size verification passed (complete model)"
    else
        echo "⚠️  Warning: Model size seems small, may be incomplete"
    fi
else
    echo "❌ Error: Training model generation failed"
    exit 1
fi

echo ""
echo "=== Step 4: Cleanup ==="
echo "Removing temporary files..."
rm -f temp_config.json revert.json
echo "✅ Cleanup completed"

echo ""
echo "=== Generation Summary ==="
echo "✅ ConvNext training model generated successfully!"
echo ""
echo "Generated files:"
echo "  🧠 $OUTPUT_TRAINING_MODEL ($MODEL_SIZE)"
echo ""
echo "Key points:"
echo "  • Uses minimal config: {\"Train\": true}"
echo "  • Generates complete training model with all parameters"
echo "  • Includes all necessary training inputs (LearningRate, etc.)"
echo "  • Ready for full ConvNext mobile training"
echo ""
echo "Next steps:"
echo "  1. Run ./1_build_convnext_android.sh to compile"
echo "  2. Run ./2_deploy_convnext_android.sh to deploy to device"
echo "  3. Start training on Android device"
echo ""
echo "Training model generation completed successfully! 🎉"
